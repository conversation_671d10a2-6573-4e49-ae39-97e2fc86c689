.holidays {

    a {
        text-decoration: none;

        h4 {
            transition: 300ms;
        }

        &:hover, &:focus {
            h4 {
                color: $blue;
            }
        }
    }

    &__inner {
        padding: 50px 0;
        border-top: 8px solid $bluegrey;
    }


    &__posts {
        padding-top: 75px;
    }

    &__post {
        padding-bottom: 40px;
        margin-bottom: 40px;
        border-bottom: 1px solid #afafaf;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding: 0 15px 40px;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }


    .holidays__post-row {
        position: relative;
        cursor: pointer;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            max-width: 360px;
            margin: 0 auto;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            margin: 0 auto;
        }

        // Create clickable overlay for entire row
        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            background: transparent;
        }

        // Ensure gallery has higher z-index to remain functional
        .holidays__gallery {
            position: relative;
            z-index: 2;
        }

        // Ensure existing links still work (title link)
        a {
            position: relative;
            z-index: 2;
        }
    }

    ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
            padding-left: 16px;
            font-size: 1.4rem;
            line-height: 1.4;

            &::before {
                content: "";
                display: inline-block;
                position: relative;
                left: -4px;
                width: 8px;
                height: 13px;
                margin-left: -12px;
                border: solid $teal;
                border-width: 0 3px 3px 0;
                transform: rotate(45deg);
            }
        }
    }

    &__gallery {
        max-width: 295px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            max-width: 360px;
            padding-bottom: 10px;
        }
    }

    &__post-content {

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            padding-bottom: 10px;
        }
    }

    &__image {
        width: 100%;

        img {
            width: 100%;
        }
    }

    .flickity-button {
        display: block;
        width: 20px;
        height: 20px;
        border: none;
        padding: 0;

        svg {
            display: block;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            fill: $white;
        }

        &.previous {
            left: 4px;
        }

        &.next {
            right: 4px;
        }
    }

    .flickity-page-dots {
        bottom: 15px;

        .dot {
            width: 4px;
            height: 4px;
            margin: 0 2px;

            &.is-selected {
                width: 6px;
                height: 6px;
            }
        }
    }

    &__title {
        margin-bottom: 2px;
    }

    &__price {
        margin-bottom: 10px;
        font-weight: 300;
    }

    &__types {
        padding-bottom: 5px;
    }

    &__text {
        display: block;
        margin-bottom: 2px;
        letter-spacing: 0.025em;
    }

    &__link-wrapper {
        padding-top: 15px;
    }

    &__link {
        min-width: 0;
        width: 100%;
    }
}
