/* general */

html,
body {
    height: 100%;
    background: $white;
    overflow-x: hidden;
}

* {
    box-sizing: border-box;
}

body {

    line-height: 1.4;
    color: $textgrey;
    font-weight: 400;
    background-color: $white;
    font-family: $bodyfontfamily;
    -webkit-font-smoothing: antialiased;
    -moz-font-smoothing: antialiased;
    -o-font-smoothing: antialiased;
    padding: 130px 0 0;

    @media only screen and (max-width: 1720px) {
        padding-top: 100px;
    }

    @media only screen and (max-width: 1160px) {
        padding-top: 80px;
    }
}

a,
input[type="submit"],
a span,
.button {
    @include transition(0.3s);
    border-radius: 0;
}

p,
li {
    color: $textgrey;
    line-height: 1.875;
    font-size: 1.6rem;
}

li {
    line-height: 1.4;
}

.copy-large {
    p,
    li {
        font-size: 2rem;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 1.6rem;
        }
    }
}

p {
    margin: 0 0 20px;
}

ul,
ol {
    margin: 0 0 20px;
}

li {
    margin-bottom: 5px;
}

a,
ul,
li {
    -webkit-tap-highlight-color: transparent;
}

a {
    font-weight: bold;
    color: $teal;
    text-decoration: underline;
}

a:hover,
a:focus {
    text-decoration: underline;
    color: $blue;
}

img {
    max-width: 100%;
    height: auto;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0 0 20px;
    line-height: 1.2;
    font-family: $headingfontfamily;
    font-weight: 400;
    color: $bluegrey;
}

h1 {
    line-height: 1.05;
    font-size: 6.6rem;

    @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
        font-size: 5rem;
    }

    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        font-size: 3rem;
    }
}

h2 {
    line-height: 1.05;
    font-size: 4rem;


    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        font-size: 3.2rem;
    }

    &.h2-large {
        font-size: 6rem;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            font-size: 4rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 3.6rem;
        }
    }

    &.h2-small {
        font-size: 3.5rem;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 3rem;
        }
    }
}

.holiday-blocks-wrapper {
    h2 {
        // font-size: 3rem;

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 2.8rem;
        }
    }
}

h3 {
    line-height: 1.1;
    font-size: 3rem;

    @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
        font-size: 2.6rem;
    }

    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        font-size: 2.4rem;
    }

    &.h3-large {
        line-height: 1.05;
        font-size: 4rem;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            font-size: 2.6rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            font-size: 2.4rem;
        }
    }
}

h4 {
    font-size: 2.4rem;

    &.h4-small {
        font-size: 2.2rem;
    }
}

h5 {
    font-size: 2rem;
}

h6 {
    font-size: 1.6rem;
}

.small {
    font-size: 1.4rem;
}

strong {
    font-weight: 700;
}

blockquote {
    line-height: 1.2;
    font-family: $headinglightsfontfamily;
    font-weight: 400;
    margin: 0;
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    li {
        line-height: 1.2;
        font-size: 2.6rem;
        color: $bluegrey;
    }
}

.container {
    margin: 0 auto;
    max-width: map-get($container-max-widths, xl);
    @media (max-width: map-get($grid-breakpoints-max, sm)) {
        width: 92%;
        padding-right: 5px;
        padding-left: 5px;

        .row {
            margin-right: -5px;
            margin-left: -5px;
        }
        div[class^="col"] {
            padding-right: 5px;
            padding-left: 5px;
        }
    }

    @media (min-width: map-get($grid-breakpoints, sm)) {
        width: 95%;
    }

    @media (min-width: map-get($grid-breakpoints, md)) {
        width: 90%;
    }

    @media (min-width: map-get($grid-breakpoints, lg)) {
        width: 90%;
    }

    @media (min-width: map-get($grid-breakpoints, xl)) {
        width: 90%;
    }
}

.inner-container {
    max-width: 800px;
    margin: 0 auto;
}

.grid-full-width {
    padding: 0;
    max-width: none;
    .row {
        margin: 0;
        div[class*="col"] {
            padding: 0;
        }
    }
}

/* Text meant only for screen readers. */
.screen-reader-text {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    word-wrap: normal !important;
}

.none {
    display: none;
}

.block {
    display: block;
}

.inline-block {
    display: inline-block;
}

.inline {
    display: inline;
}

.table {
    display: table;
}

.table-cell {
    display: table-cell;
}

.flex {
    display: flex;
    flex-wrap: wrap;
}

.grid {
    display: grid;
}

.centre {
    text-align: center;
}

.vmiddle {
    vertical-align: middle;
}

.font-zero {
    font-size: 0;
}

.capitilize {
    text-transform: capitalize;
}

.content-area {
    & > * {
        &:last-child {
            margin-bottom: 0;
        }
    }
}

.heading-underlined {
    &:after {
        content: "";
        display: block;
        width: 90px;
        height: 10px;
        margin: 20px auto 0;
        background: url(../img/heading-underline.svg) center no-repeat;
    }
}

.heading-light {
    font-family: $headinglightsfontfamily;
}

.body-text {
    font-family: $bodyfontfamily;
}

.text-weight-hairline {
    font-weight: 100;
}

.text-weight-thin {
    font-weight: 200;
}

.text-weight-light {
    font-weight: 300;
}

.text-weight-regular {
    font-weight: 400;
}

.text-weight-medium {
    font-weight: 500;
}

.text-weight-semibold {
    font-weight: 600;
}

.text-weight-bold {
    font-weight: 700;
}

.text-weight-extrabold {
    font-weight: 800;
}

.text-weight-black {
    font-weight: 900;
}

.text-uppercase {
    text-transform: uppercase;
}

.text-italic {
    font-style: italic;
}

::placeholder {
    color: $bluegrey;
    opacity: 1;
}

:-ms-input-placeholder {
    color: $bluegrey;
}

::-ms-input-placeholder {
    color: $bluegrey;
}

.text-white {
    color: $white;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    li {
        color: $white;
    }

    p,
    li {
        a {
            color: $white;

            &:hover,
            &:focus {
                color: rgba($white, 0.75);
            }
        }
    }
}

.text-teal {
    color: $teal;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    li {
        color: $teal;
    }

    p,
    li {
        a {
            color: $teal;

            &:hover,
            &:focus {
                color: rgba($teal, 0.75);
            }
        }
    }
}

.text-blue {
    color: $blue;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    li {
        color: $blue;
    }

    p,
    li {
        a {
            color: $blue;

            &:hover,
            &:focus {
                color: rgba($blue, 0.75);
            }
        }
    }
}

.text-black {
    color: $black;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    li {
        color: $black;
    }

    p,
    li {
        a {
            color: $black;

            &:hover,
            &:focus {
                color: rgba($black, 0.75);
            }
        }
    }
}

.bg-white {
    position: relative;

    &:after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: -500%;
        z-index: 0;
        width: 1000%;
        height: 100%;
        background: $white;
    }

}

.bg-grey {
    position: relative;

    &:after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: -500%;
        z-index: 0;
        width: 1000%;
        height: 100%;
        background: $offwhitethree;
    }

}

.button {
    @include brandbutton;
}

.social-button {
    display: inline-block;
    padding: 12px;
    line-height: 1;
    font-family: $headingfontfamily;
    font-size: 2rem;
    font-weight: 500;
    text-decoration: none;
    border-radius: 5px;
    border: 1px solid $bluegrey;
    color: $bluegrey;

    &:hover,
    &:focus {
        text-decoration: none;
        border-color: $blue;
        color: $blue;
    }

    &__icon {
        display: inline-block;
        vertical-align: middle;
        max-width: 24px;
        margin-right: 13px;
    }

    &__text {
        display: inline-block;
        vertical-align: middle;
        transition: none;
    }
}

.link {
    font-weight: 600;
    color: $teal;

    .link-text {
        text-decoration: underline;
        transition: none;
    }

    svg {
        margin-left: 2px;
        font-size: 1.2rem;
        transition: 300ms;
    }

    &:hover,
    &:focus {
        color: $blue;
    }
}

/* general elements */

.page-wrapper {
    padding: 0 25px;

    @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
        padding: 0 15px;
    }

    @media only screen and (max-width: 1160px) {
        padding: 0;
    }
}

.overflow-menu {
    position: relative;
    height: 100%;
    overflow: hidden;
}

.loader {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 15px;
    margin: auto;
    display: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    border-top: 4px solid $teal;
    border-right: 4px solid $teal;
    border-bottom: 4px solid transparent;
    border-left: 4px solid transparent;
    animation: spin 500ms infinite linear;

    @keyframes spin {
        0% {
            transform: rotate(0);
        }
        100% {
            transform: rotate(360deg);
        }
    }
}

.back-to-top {
    &:hover,
    &:focus {
        .back-to-top__content {
            background: $teal;
        }
    }

    &__content {
        padding: 13px 0;
        text-align: center;
        font-family: $headingfontfamily;
        font-size: 2.4rem;
        background: $pink;
        color: $white;
        transition: 300ms;
        cursor: pointer;
    }

    &__text {
        margin: 0 6px;
    }
}

.next-posts-link {
    display: none;
}

.cat {
    margin-bottom: 5px;

    &__icon {
        display: inline-block;
        vertical-align: middle;
        position: relative;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        color: $white;

        svg {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            margin: auto;
            font-size: 1.9rem;
        }
    }

    &__text {
        display: inline-block;
        margin-left: 5px;
        vertical-align: middle;
        font-family: $headingfontfamily;
        font-weight: 500;
        font-size: 1.4rem;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        color: $bluegrey;
    }
}

.desktop-only {
    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        display: none;
    }
}

.mobile-only {
    display: none;
    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        display: block;
    }
}

.overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 99999;
    width: 100%;
    height: 100%;
    margin: auto;
    background: $white;
}

/* Google Maps
============== */

.acf-map {
    width: 100%;
    height: 630px;

    @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
        height: 400px;
    }
}

/* fixes potential theme css conflict */
.acf-map img {
    max-width: inherit !important;
}

/* Flickity
=========== */

.flickity-enabled {
    .flickity-button {
        width: 75px;
        height: 75px;
        border-radius: 50%;
        border: 1px solid $bluegrey;
        font-size: 3rem;
        color: $bluegrey;
        background: none;
        box-shadow: none;
        transition: 300ms;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            width: 60px;
            height: 60px;
            font-size: 2.6rem;
        }

        @media only screen and (max-width: map-get($grid-breakpoints-max, md)) {
            display: none;
        }

        &:hover,
        &:focus {
            color: $teal;
            border-color: $teal;
        }

        .flickity-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            margin: auto;
            &--prev {
                right: 5px;
            }
            &--next {
                left: 5px;
            }
        }

        > svg {
            display: none;
        }
        &.previous {
            left: -35px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                left: -20px;
            }
        }

        &.next {
            right: -35px;

            @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
                right: -20px;
            }
        }
    }
}

.flickity-page-dots {
    bottom: 20px;

    .dot {
        width: 9px;
        height: 9px;
        margin: 0 5px;
        vertical-align: middle;
        opacity: 0.5;
        background: $white;
        transition: 300ms;

        &.is-selected {
            width: 13px;
            height: 13px;
        }
    }
}

.paginate-links {
    padding: 75px 0;

    @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
        padding: 30px 0;
    }
}

.no-results {
    padding: 75px 0;
}

.noUi-target {
    .noUi-handle {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        box-shadow: none;
        background: $blue;
        border: none;

        &:before,
        &:after {
            display: none;
        }
    }

    &.noUi-horizontal {
        height: 2px;
        box-shadow: none;
    }
}

html:not([dir="rtl"]) .noUi-horizontal .noUi-handle {
    top: -9px;
    right: -10px;
    outline: 0;
    cursor: grab;
}

/* Accordions
============= */

.accordion {
    margin: 0 -15px;

    &__item {
        position: relative;
        flex: 0 0 50%;
        max-width: 50%;
        padding: 30px 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            flex: 0 0 100%;
            max-width: 100%;
        }

        &.active {
            .link {
                color: $blue;

                svg {
                    transform: rotate(180deg);
                }
            }
        }

        &:nth-child(2n + 1) {
            &:after {
                left: 15px;

                @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                    left: 0;
                }
            }
        }

        &:after {
            content: "";
            display: block;
            position: absolute;
            bottom: 0;
            left: -15px;
            width: 100%;
            height: 1px;
            background: $midlightgrey;

            @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
                left: 0;
            }
        }

        &:last-child,
        &:nth-last-child(2) {
            &:after {
                display: none;
            }
        }
    }

    &__heading-wrapper {
        cursor: pointer;
        &:hover,
        &:focus {
            .link {
                color: $blue;
            }
        }
    }

    &__heading-text {
        flex: 1 0;
        padding: 0 0 0 30px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, sm)) {
            padding-left: 15px;
        }
    }

    &__heading {
        margin-bottom: 5px;
    }

    &__copy-wrapper {
        position: relative;
    }

    &__copy {
        padding-top: 15px;
    }

    &__close {
        position: absolute;
        top: 15px;
        right: 20px;
        font-size: 2.4rem;
        color: $bluegrey;
        cursor: pointer;
    }
}

.list-arrow {
    display: block;
    position: relative;
    padding-left: 1.5em;
    text-indent: -1.5em;

    &:before {
        content: "";
        display: inline-block;
        width: 16px;
        height: 11px;
        margin: auto 8px auto 0;
        background: url(../img/arrow.svg) center no-repeat;
        background-size: contain;
    }

    &--cross {
        &:before {
            background: url(../img/cross.svg) center no-repeat;
            background-size: contain;
        }
    }
}

/* Datepicker
============= */

.datepicker-top-left,
.datepicker-top-right {
    border-top-color: $teal;
}

.datepicker-top-left::before,
.datepicker-top-right::before {
    border-bottom-color: $teal;
}

.datepicker-bottom-left,
.datepicker-bottom-right {
    border-bottom-color: $teal;
}

.datepicker-bottom-left::before,
.datepicker-bottom-right::before {
    border-top-color: $teal;
}

.datepicker-panel > ul > li.picked,
.datepicker-panel > ul > li.picked:hover {
    color: $teal;
}

/* Tooltip
========== */

.tooltip {
    position: relative;
    top: -1px;
    z-index: 30;
    display: inline-block;

    img {
        max-width: 15px;
    }

    &:hover,
    &:focus,
    &.active {
        .tooltip__label {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
            transform: translateY(-115%) translateX(-50%);
        }
    }

    &__label {
        display: block;
        position: absolute;
        top: 0;
        left: 50%;
        width: auto;
        padding: 4px 8px;
        max-width: none;
        border-radius: 5px;
        background: $bluegrey;
        font-size: 1.4rem;
        font-weight: 500 !important;
        color: $white;
        white-space: nowrap;
        text-align: center;
        transform: translateY(-100%) translateX(-50%);
        opacity: 0;
        pointer-events: none;
        visibility: hidden;
        transition: 300ms;
        color: $white !important;

        &:after {
            content: "";
            display: block;
            position: absolute;
            top: 100%;
            right: 0;
            left: 0;
            width: 0;
            height: 0;
            margin: 0 auto;
            border-top: 5px solid $bluegrey;
            border-right: 5px solid transparent;
            border-bottom: 0 solid transparent;
            border-left: 5px solid transparent;
        }
    }
}

/* AITO Widget
============== */

#aito-widget {
    img {
        max-height: 35px;
    }
}


img.alignright { float: right; margin: 0 0 1em 1em; }
img.alignleft { float: left; margin: 0 1em 1em 0; }
img.aligncenter { display: block; margin-left: auto; margin-right: auto; }
.alignright { float: right; }
.alignleft { float: left; }
.aligncenter { display: block; margin-left: auto; margin-right: auto; }


.accordion__button-wrapper {
    text-align: right;
}

.accordion__button {
    &:hover, &:focus {
        background: $blue;
        color: $white;
    }
}


/* DatePicker Container */
.ui-datepicker {
    width: 216px;
    height: auto;
    margin: 5px auto 0;
    font: 9pt Arial, sans-serif;
    -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, .5);
    -moz-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, .5);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, .5);
    background: $white;
}
.ui-datepicker a {
    text-decoration: none;
}
/* DatePicker Table */
.ui-datepicker table {
    width: 100%;
}
.ui-datepicker-header {
    background: $teal;
    color: #e0e0e0;
    font-weight: bold;
    -webkit-box-shadow: inset 0px 1px 1px 0px rgba(250, 250, 250, 2);
    -moz-box-shadow: inset 0px 1px 1px 0px rgba(250, 250, 250, .2);
    box-shadow: inset 0px 1px 1px 0px rgba(250, 250, 250, .2);
    text-shadow: 1px -1px 0px #000;
    filter: dropshadow(color=#000, offx=1, offy=-1);
    line-height: 30px;
    border-width: 1px 0 0 0;
    border-style: solid;
    border-color: #111;
}
.ui-datepicker-title {
    text-align: center;
}
.ui-datepicker-prev, .ui-datepicker-next {
    display: inline-block;
    width: 30px;
    height: 30px;
    margin: 0 5px;
    text-align: center;
    cursor: pointer;
    background-image: url('../img/arrow-left.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-color: $teal;
    line-height: 600%;
    overflow: hidden;
}
.ui-datepicker-next {
    background-image: url('../img/arrow-right.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    background-color: $teal;
}
.ui-datepicker-next {
    float: right;
}
.ui-datepicker thead {
    background-color: #f7f7f7;

    border-bottom: 1px solid #bbb;
}
.ui-datepicker th {
    text-transform: uppercase;
    font-size: 6pt;
    padding: 5px 0;
    color: #666666;
    text-shadow: 1px 0px 0px #fff;
    filter: dropshadow(color=#fff, offx=1, offy=0);
}
.ui-datepicker tbody td {
    padding: 0;
    border-right: 1px solid #bbb;
}
.ui-datepicker tbody td:last-child {
    border-right: 0px;
}
.ui-datepicker tbody tr {
    border-bottom: 1px solid #bbb;
}
.ui-datepicker tbody tr:last-child {
    border-bottom: 0px;
}
.ui-datepicker td span, .ui-datepicker td a {
    display: inline-block;
    font-weight: bold;
    text-align: center;
    width: 30px;
    height: 30px;
    line-height: 30px;
    color: #666666;
    text-shadow: 1px 1px 0px #fff;
    filter: dropshadow(color=#fff, offx=1, offy=1);
}
.ui-datepicker-calendar .ui-state-default {
    background: #ededed;

    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ededed', endColorstr='#dedede',GradientType=0 );
    -webkit-box-shadow: inset 1px 1px 0px 0px rgba(250, 250, 250, .5);
    -moz-box-shadow: inset 1px 1px 0px 0px rgba(250, 250, 250, .5);
    box-shadow: inset 1px 1px 0px 0px rgba(250, 250, 250, .5);
}
.ui-datepicker-calendar .ui-state-hover {
    background: #f7f7f7;
}
.ui-datepicker-calendar .ui-state-active {
    background: #6eafbf;
    -webkit-box-shadow: inset 0px 0px 10px 0px rgba(0, 0, 0, .1);
    -moz-box-shadow: inset 0px 0px 10px 0px rgba(0, 0, 0, .1);
    box-shadow: inset 0px 0px 10px 0px rgba(0, 0, 0, .1);
    color: #e0e0e0;
    text-shadow: 0px 1px 0px $teal;
    border: 1px solid $teal;
    position: relative;
}
.ui-datepicker-unselectable .ui-state-default {
    background: #f4f4f4;
    color: #b4b3b3;
}
.ui-datepicker-calendar td:first-child .ui-state-active {
    width: 29px;
    margin-left: 0;
}
.ui-datepicker-calendar td:last-child .ui-state-active {
    width: 29px;
    margin-right: 0;
}
.ui-datepicker-calendar tr:last-child .ui-state-active {
    height: 29px;
    margin-bottom: 0;
}

.ui-datepicker-month, .ui-datepicker-year {
    display: inline-block;
    width: 50%;
    border: none;
     background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%2301a59f'><polygon points='0,0 100,0 50,50'/></svg>");
     background-size: 12px;
     background-position: calc(100% - 10px) center;
     background-repeat: no-repeat;
     background-color: $white;
}

.geoip_country { display:none !important; }