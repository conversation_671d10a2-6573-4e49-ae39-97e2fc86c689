<?php
/**
 * Steps Ahead Panel Component
 * Displays steps ahead content on listing pages after the bottom CTA
 * Content comes from Theme Settings for most pages, All Holidays archive has its own content
 */

$page_obj = get_queried_object();

// DEBUG: Show initial values before any processing
?>
<!-- DEBUG: Steps Ahead Panel Initial Values -->
<pre style="background: #f0f0f0; padding: 20px; margin: 20px 0; font-size: 12px; overflow: auto;">
<strong>DEBUG - Steps Ahead Panel Initial Check:</strong>

<strong>Page Type Detection:</strong>
is_tax('holiday-type'): <?php echo is_tax('holiday-type') ? 'TRUE' : 'FALSE'; ?>
is_tax('holiday-regions'): <?php echo is_tax('holiday-regions') ? 'TRUE' : 'FALSE'; ?>
is_post_type_archive('holiday'): <?php echo is_post_type_archive('holiday') ? 'TRUE' : 'FALSE'; ?>

<strong>Current URL:</strong> <?php echo $_SERVER['REQUEST_URI']; ?>

<strong>Page Object:</strong>
<?php var_dump($page_obj); ?>

<strong>Queried Object Type:</strong> <?php echo get_class($page_obj); ?>

<?php if (isset($page_obj->taxonomy)) : ?>
<strong>Taxonomy:</strong> <?php echo $page_obj->taxonomy; ?>
<strong>Term Slug:</strong> <?php echo $page_obj->slug; ?>
<?php endif; ?>
</pre>
<?php
$panel_settings = null;
$panel_title = '';
$panel_copy = '';
$panel_columns = [];

// Check if we're on a supported page type and get the appropriate settings
if (is_tax('holiday-type')) {
    // Holiday Type taxonomy page
    $panel_settings = get_field('steps_ahead_panel', $page_obj);

    // Get global content from Theme Settings
    $global_content = get_field('steps_ahead_global_content', 'option');
    if ($global_content && $global_content['panel_status']) {
        $panel_title = $global_content['panel_title'] ?: '';
        $panel_columns = $global_content['columns'] ?: [];
    }

} elseif (is_tax('holiday-regions')) {
    // Holiday Regions taxonomy page
    $panel_settings = get_field('steps_ahead_panel', $page_obj);

    // Get global content from Theme Settings
    $global_content = get_field('steps_ahead_global_content', 'option');

    // DEBUG: Show holiday-regions specific values
    ?>
    <pre style="background: #e0f0ff; padding: 20px; margin: 20px 0; font-size: 12px; overflow: auto;">
    <strong>DEBUG - Holiday Regions Branch:</strong>

    <strong>Panel Settings from Term:</strong>
    <?php var_dump($panel_settings); ?>

    <strong>Global Content from Theme Settings:</strong>
    <?php var_dump($global_content); ?>

    <strong>Global Content Panel Status:</strong>
    <?php
    if ($global_content) {
        echo "Global content exists\n";
        echo "Panel status: " . var_export($global_content['panel_status'] ?? 'NOT SET', true) . "\n";
    } else {
        echo "Global content is NULL/FALSE\n";
    }
    ?>
    </pre>
    <?php

    if ($global_content && $global_content['panel_status']) {
        $panel_title = $global_content['panel_title'] ?: '';
        $panel_columns = $global_content['columns'] ?: [];
    }

} elseif (is_post_type_archive('holiday')) {
    // All Holidays archive page - get styling settings from archive page, content from Theme Settings
    $panel_settings = get_field('steps_ahead_panel', 'option');

    // Get global content from Theme Settings
    $global_content = get_field('steps_ahead_global_content', 'option');
    if ($global_content && $global_content['panel_status']) {
        $panel_title = $global_content['panel_title'] ?: '';
        $panel_columns = $global_content['columns'] ?? [];
    }

} else {
    // Not a supported page type
    return;
}

// Check if panel is enabled - all pages now use 'panel_status'
// Default to enabled (true) if no value is saved
$panel_enabled = isset($panel_settings['panel_status']) ? $panel_settings['panel_status'] : true;

// DEBUG: Show final check values
?>
<pre style="background: #fff0e0; padding: 20px; margin: 20px 0; font-size: 12px; overflow: auto;">
<strong>DEBUG - Final Panel Checks:</strong>

<strong>Panel Enabled Check:</strong>
Panel Settings Exists: <?php echo $panel_settings ? 'TRUE' : 'FALSE'; ?>
Panel Enabled Value: <?php echo var_export($panel_enabled, true); ?>
Will Return Early (panel check): <?php echo (!$panel_settings || !$panel_enabled) ? 'TRUE' : 'FALSE'; ?>

<strong>Panel Content Check:</strong>
Panel Title: <?php echo var_export($panel_title ?? 'NOT SET', true); ?>
Panel Columns: <?php echo var_export($panel_columns ?? 'NOT SET', true); ?>
Panel Columns Count: <?php echo is_array($panel_columns ?? null) ? count($panel_columns) : 'NOT ARRAY'; ?>
</pre>
<?php

if (!$panel_settings || !$panel_enabled) {
    echo '<p style="background: red; color: white; padding: 10px;">RETURNING EARLY: Panel settings or panel enabled check failed</p>';
    return;
}

// Only return if explicitly disabled - allow showing even with no columns for debugging
// if (!$panel_columns || empty($panel_columns)) {
//     return;
// }

// Use individual page settings for styling - all pages now use the same field names
// Set defaults for listing pages when no values are saved
$background_color = $panel_settings['panel_background'] ?: '#ebf2f1';
$curved_edge = $panel_settings['curve_position'] ?: 'bottom';
$flip_curved_edge = $panel_settings['curve_flip'] ?: false;
$point_title_color = $panel_settings['point_title'] ?: '';
$column_background_color = $panel_settings['point_background'] ?: '#ffffff'; // White background by default

// Set up background classes
$background_classes = '';
$has_background = $background_color && $background_color !== 'transparent' && $background_color !== '';
if ($has_background) {
    $background_classes .= ' has-background';
}
if ($curved_edge && $curved_edge !== 'none') {
    $background_classes .= ' curve-' . $curved_edge;
    if ($flip_curved_edge) {
        $background_classes .= ' curve-flipped';
    }
}

?>

<!-- DEBUG: Steps Ahead Panel Values -->
<pre style="background: #f0f0f0; padding: 20px; margin: 20px 0; font-size: 12px; overflow: auto;">
<strong>DEBUG - Steps Ahead Panel Values:</strong>

<strong>Page Type Detection:</strong>
is_tax('holiday-type'): <?php echo is_tax('holiday-type') ? 'TRUE' : 'FALSE'; ?>
is_tax('holiday-regions'): <?php echo is_tax('holiday-regions') ? 'TRUE' : 'FALSE'; ?>
is_post_type_archive('holiday'): <?php echo is_post_type_archive('holiday') ? 'TRUE' : 'FALSE'; ?>

<strong>Page Object:</strong>
<?php var_dump($page_obj); ?>

<strong>Panel Settings (from page/term):</strong>
<?php var_dump($panel_settings); ?>

<strong>Global Content (from Theme Settings):</strong>
<?php var_dump($global_content); ?>

<strong>Processed Values:</strong>
Panel Title: <?php echo var_export($panel_title, true); ?>
Panel Columns Count: <?php echo is_array($panel_columns) ? count($panel_columns) : 'NOT ARRAY'; ?>
Panel Enabled: <?php echo var_export($panel_enabled, true); ?>
Background Color: <?php echo var_export($background_color, true); ?>
Column Background Color: <?php echo var_export($column_background_color, true); ?>

<strong>Final Checks:</strong>
!$panel_settings: <?php echo !$panel_settings ? 'TRUE' : 'FALSE'; ?>
!$panel_enabled: <?php echo !$panel_enabled ? 'TRUE' : 'FALSE'; ?>
!$panel_columns || empty($panel_columns): <?php echo (!$panel_columns || empty($panel_columns)) ? 'TRUE' : 'FALSE'; ?>
</pre>

<section class="steps-ahead steps-ahead--listing">
    <div class="steps-ahead__inner<?php echo $background_classes ? ' ' . $background_classes : ''; ?>" data-aos="fade"<?php if ($has_background) : ?> style="background-color: <?php echo $background_color; ?>;"<?php endif; ?>>
        <div class="container steps-ahead__container">
            <div class="steps-ahead__content centre inner-container">
                <?php if($panel_title) : ?>
                    <h2 class="steps-ahead__heading h2-large heading-light heading-underlined text-weight-regular"><?php echo esc_html($panel_title); ?></h2>
                <?php endif; ?>
                <?php if($panel_copy) : ?>
                    <div class="steps-ahead__copy content-area copy-large">
                        <?php echo $panel_copy; ?>
                    </div>
                <?php endif; ?>
            </div>

            <?php if($panel_columns) : ?>
                <div class="row steps-ahead__row">
                    <?php foreach($panel_columns as $column) : ?>
                        <?php
                        // Handle field structure from Theme Settings:
                        // Theme Settings: title, image, points (with title, text)
                        $ctitle = $column['title'] ?: '';
                        $cimage = $column['image'] ?: null;
                        $cpoints = $column['points'] ?: [];
                        ?>

                        <div class="col-md-3 steps-ahead__col">
                            <div class="steps-ahead__col-content centre"<?php if($column_background_color) : ?> style="background-color: <?php echo $column_background_color; ?>;"<?php endif; ?>>
                                <?php if($cimage) : ?>
                                    <div class="steps-ahead__col-image">
                                        <img src="<?php echo $cimage['url']; ?>" alt="<?php echo $cimage['alt'] ?: ''; ?>">
                                        <?php if($ctitle) : ?>
                                            <h4 class="steps-ahead__col-heading"><?php echo esc_html($ctitle); ?></h4>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if($cpoints && !empty($cpoints)) : ?>
                                    <div class="steps-ahead__col-points">
                                        <?php foreach($cpoints as $point) : ?>
                                            <?php
                                            $point_title = $point['title'] ?: '';
                                            $point_text = $point['text'] ?: '';
                                            ?>
                                            <?php if($point_title || $point_text) : ?>
                                                <div class="steps-ahead__point">
                                                    <?php if($point_title) : ?>
                                                        <h5 class="steps-ahead__point-title"<?php if($point_title_color) : ?> style="color: <?php echo $point_title_color; ?>;"<?php endif; ?>><?php echo esc_html($point_title); ?></h5>
                                                    <?php endif; ?>
                                                    <?php if($point_text) : ?>
                                                        <div class="steps-ahead__point-text">
                                                            <?php echo nl2br(esc_html($point_text)); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section><!-- .steps-ahead -->
