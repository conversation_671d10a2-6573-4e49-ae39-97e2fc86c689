<?php
$test_groups = get_field( 'test_group', 'options' );
$modals = [];
if ( is_array( $test_groups ) ) {
  foreach ( $test_groups as $test_group ) {
    if ( is_array( $test_group ) && isset( $test_group['acf_fc_layout'] ) && $test_group['acf_fc_layout'] === 'form_modal' && isset( $test_group['enabled'] ) && $test_group['enabled'] ) {
      $modals = $test_group['Form Modal'];
      break;
    }
  }
}
?>
<?php if ( $modals ): ?>
<div class="form-modal-ab" id="form-modal-ab-variant-1"></div>
<div class="modal-variants" class="form-modal" style="display:none">
  <?php foreach ( $modals as $idx => $modal ): ?>
    <div id="modal-variant-<?= ($idx + 1); ?>" class="form-modal">
      <?php if ( $modal['layout'] === 'one-column' ): ?>
      <div class="content <?= $modal['layout']; ?>">
        <div class="image">
          <?= wp_get_attachment_image( $modal['image']['ID'], 'holiday_type_large' ); ?>
        </div>
        <?php if ($modal['hover_image']): ?>
        <div class="image hover">
          <?= wp_get_attachment_image( $modal['hover_image']['ID'], 'holiday_type_large' ); ?>
        </div>
        <?php endif; ?>
        <div class="content-overlay">
          <?= $modal['content']; ?>
          <?= do_shortcode('[gravityform id="' . $modal['gravity_forms'] .'" title="false" description="false" ajax="true"]'); ?>
        </div>
      </div>
      <?php endif; ?>
      <?php if ( $modal['layout'] === 'two-column' ): ?>
      <div class="content <?= $modal['layout']; ?>">
        <div class="row">
          <div class="col-md-6">
            <div class="image">
              <?= wp_get_attachment_image( $modal['image']['ID'], 'holiday_type_large' ); ?>
            </div>
          </div>
          <div class="col-md-6">
            <div class="content-inline">
              <?= $modal['content']; ?>
              <?= do_shortcode('[gravityform id="' . $modal['gravity_forms'] .'" title="false" description="false" ajax="true"]'); ?>
            </div>
          </div>
        </div>
      </div>
      <?php endif; ?>
    </div>
  <?php endforeach; ?>
</div>

<script>
    (function($) {

        $(function() {

            if (sessionStorage.getItem('modalWasShown') !== 'true') {
                setTimeout(function() {
                    var modal = detectModal();
                    if (modal) {
                        sessionStorage.setItem('modalWasShown', 'true');
                        openModal(modal);
                    }
                }, 20000);
            }

        });

        function detectModal() {
            // Detect whether Google Optimize has injected a div with an ID of
            // either variant-1 or variant-2 into the page
            var ab = $(".form-modal-ab");
            //form-modal-ab-variant-1
            if (ab.length) {
                return $("#modal-" + (ab.attr("id").replace('form-modal-ab-', '')));
            }
        }

        function openModal(modal) {
            $.fancybox.open( modal, {
                type: "content",
                hideOnOverlayClick: false,
                hideOnContentClick: false
            } );
        }

    })(window.jQuery);
</script>
<?php endif; ?>
