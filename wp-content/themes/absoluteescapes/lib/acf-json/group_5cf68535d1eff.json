{"key": "group_5cf68535d1eff", "title": "Holiday Type Details", "fields": [{"key": "field_5cf6853c354ff", "label": "Image", "name": "holiday_type_image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "id", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5e593f8c722dc", "label": "Listing Image", "name": "holiday_type_listing_image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "id", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5d0cb7235fa41", "label": "Heading", "name": "holiday_type_heading", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5d0cb73e28825", "label": "Copy", "name": "holiday_type_copy", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_5d0cb75efc57c", "label": "Top Links", "name": "holiday_type_top_links", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 0, "layout": "table", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_5d0cb76efc57d", "label": "Link", "name": "link", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "parent_repeater": "field_5d0cb75efc57c"}]}, {"key": "field_5cfe1a9c175c5", "label": "Colour", "name": "holiday_type_colour", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "#000000", "enable_opacity": false, "return_format": "string"}, {"key": "field_5cfe1935765b8", "label": "Icon Font Awesome", "name": "holiday_type_icon_fa", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5cfe19cb765b9", "label": "Icon", "name": "holiday_type_icon", "aria-label": "", "type": "image", "instructions": "Overrides the Font Awesome icon with an image", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_675c163ab5b45", "label": "Top CTA", "name": "top_cta", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c16acb5b49", "label": "Status", "name": "status", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Active", "default_value": 1, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c164cb5b46", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c16acb5b49", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Talk to our travel experts", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c18daa4956", "label": "Text", "name": "text", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c16acb5b49", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Our specialists combine first-hand experience with meticulous planning to craft holidays unique to you. Enquire today to plan your perfect escape.", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c1904a4957", "label": "Button label", "name": "button_label", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c16acb5b49", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Make an Enquiry", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c191ba4958", "label": "Button link", "name": "button_link", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c16acb5b49", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "/make-an-enquiry/", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c1944a4959", "label": "Icon", "name": "icon", "aria-label": "", "type": "text", "instructions": "HTML/SVG only.", "required": 0, "conditional_logic": [[{"field": "field_675c16acb5b49", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "<svg class=\"svg-inline--fa fa-phone fa-w-16\" aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fas\" data-icon=\"phone\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" data-fa-i2svg=\"\"><path fill=\"currentColor\" d=\"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z\"></path></svg>", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c197da495b", "label": "Contact number", "name": "contact_number", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c16acb5b49", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "+44 (0) ************", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c1a96b81fb", "label": "Use Image", "name": "use_image", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c16acb5b49", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c1993a495c", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "If not set, a default image will be used.", "required": 0, "conditional_logic": [[{"field": "field_675c16acb5b49", "operator": "==", "value": "1"}, {"field": "field_675c1a96b81fb", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}]}, {"key": "field_675c166db5b47", "label": "Bottom CTA", "name": "bottom_cta", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c167eb5b48", "label": "Status", "name": "status", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Active", "default_value": 1, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c1732e243d", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c167eb5b48", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Your journey starts here", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c175de243e", "label": "Text", "name": "text", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c167eb5b48", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "We create award-winning holidays rooted in local knowledge. Enquire now for expert advice and a bespoke itinerary tailored just for you.", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c1775e243f", "label": "Button label", "name": "button_label", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c167eb5b48", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Make an Enquiry", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c1794e2440", "label": "Button link", "name": "button_link", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c167eb5b48", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "/make-an-enquiry/", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c17b5e2441", "label": "Icon", "name": "icon", "aria-label": "", "type": "text", "instructions": "HTML/SVG only.", "required": 0, "conditional_logic": [[{"field": "field_675c167eb5b48", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "<svg class=\"svg-inline--fa fa-phone fa-w-16\" aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fas\" data-icon=\"phone\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" data-fa-i2svg=\"\"><path fill=\"currentColor\" d=\"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z\"></path></svg>", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c17c8e2442", "label": "Contact number", "name": "contact_number", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c167eb5b48", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "+44 (0) ************", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c1b5096dba", "label": "Use Image", "name": "use_image", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c167eb5b48", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c17d8e2443", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "If not set, a default image will be used.", "required": 0, "conditional_logic": [[{"field": "field_675c167eb5b48", "operator": "==", "value": "1"}, {"field": "field_675c1b5096dba", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}]}, {"key": "field_675c2000f1234", "label": "Featured Holidays Panel", "name": "featured_holidays_panel", "aria-label": "", "type": "group", "instructions": "Panel that appears between the top CTA and holiday listings", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c2001f1235", "label": "Panel Status", "name": "panel_status", "aria-label": "", "type": "true_false", "instructions": "Show the featured holidays panel on this holiday type page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Show featured holidays panel", "default_value": 0, "ui_on_text": "On", "ui_off_text": "Off", "ui": 1}, {"key": "field_675c2002f1236", "label": "Panel Title", "name": "panel_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c2001f1235", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Our most popular holidays", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c2003f1237", "label": "Featured Holidays", "name": "featured_holidays", "aria-label": "", "type": "repeater", "instructions": "Select up to 4 holidays to feature", "required": 0, "conditional_logic": [[{"field": "field_675c2001f1235", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "min": 1, "max": 4, "layout": "table", "button_label": "Add Holiday", "collapsed": "", "rows_per_page": 20, "sub_fields": [{"key": "field_675c2004f1238", "label": "Holiday", "name": "holiday", "aria-label": "", "type": "post_object", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["holiday"], "taxonomy": "", "return_format": "object", "multiple": 0, "allow_null": 0, "ui": 1, "bidirectional_target": [], "parent_repeater": "field_675c2003f1237"}]}]}, {"key": "field_675c4000f3001", "label": "Steps Ahead Panel", "name": "steps_ahead_panel", "aria-label": "", "type": "group", "instructions": "Content is managed centrally in Theme Settings. Only style controls are available here.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c4001f3002", "label": "Panel Status", "name": "panel_status", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 1, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_675c4004f3005", "label": "Panel Background", "name": "panel_background", "aria-label": "", "type": "color_picker", "instructions": "Choose a background color for the panel", "required": 0, "conditional_logic": [[{"field": "field_675c4001f3002", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "#ebf2f1", "enable_opacity": 0, "return_format": "string"}, {"key": "field_675c4005f3006", "label": "Curve Position", "name": "curve_position", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c4001f3002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"none": "None", "top": "Top", "bottom": "Bottom"}, "default_value": "bottom", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_68557153e7952", "label": "<PERSON><PERSON>ve <PERSON>", "name": "curve_flip", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_685570318ae11", "label": "Point Title", "name": "point_title", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "#75ada4", "enable_opacity": 0, "return_format": "string"}, {"key": "field_6855705a8ae12", "label": "Point Background", "name": "point_background", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "enable_opacity": 0, "return_format": "string"}]}], "location": [[{"param": "taxonomy", "operator": "==", "value": "holiday-type"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1751034088}