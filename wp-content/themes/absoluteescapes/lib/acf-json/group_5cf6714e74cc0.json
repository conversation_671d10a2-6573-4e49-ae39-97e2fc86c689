{"key": "group_5cf6714e74cc0", "title": "Holiday Region Details", "fields": [{"key": "field_5cf6715773333", "label": "Image", "name": "region_image", "aria-label": "", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "id", "preview_size": "thumbnail", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_5cff6a91feb28", "label": "Heading", "name": "region_heading", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}, {"key": "field_5cff6aa0feb29", "label": "Copy", "name": "region_copy", "aria-label": "", "type": "wysiwyg", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "tabs": "all", "toolbar": "full", "media_upload": 1, "delay": 0}, {"key": "field_5cff6f0e0ebbf", "label": "Top Links", "name": "region_top_links", "aria-label": "", "type": "repeater", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 0, "max": 2, "layout": "table", "button_label": "Add Row", "rows_per_page": 20, "sub_fields": [{"key": "field_5cff6f1d0ebc0", "label": "Link", "name": "link", "aria-label": "", "type": "link", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "parent_repeater": "field_5cff6f0e0ebbf"}]}, {"key": "field_675c25ebe1bc2", "label": "Top CTA", "name": "top_cta", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c25ebe1bc3", "label": "Status", "name": "status", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Active", "default_value": 1, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c25ebe1bc4", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c25ebe1bc3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Talk to our travel experts", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c25ebe1bc5", "label": "Text", "name": "text", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c25ebe1bc3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Our specialists combine first-hand experience with meticulous planning to craft holidays unique to you. Enquire today to plan your perfect escape.", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c25ebe1bc6", "label": "Button label", "name": "button_label", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c25ebe1bc3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Make an Enquiry", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c25ebe1bc7", "label": "Button link", "name": "button_link", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c25ebe1bc3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "/make-an-enquiry/", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c25ebe1bc8", "label": "Icon", "name": "icon", "aria-label": "", "type": "text", "instructions": "HTML/SVG only.", "required": 0, "conditional_logic": [[{"field": "field_675c25ebe1bc3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "<svg class=\"svg-inline--fa fa-phone fa-w-16\" aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fas\" data-icon=\"phone\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" data-fa-i2svg=\"\"><path fill=\"currentColor\" d=\"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z\"></path></svg>", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c25ebe1bc9", "label": "Contact number", "name": "contact_number", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c25ebe1bc3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "+44 (0) ************", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c25ebe1bca", "label": "Use Image", "name": "use_image", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c25ebe1bc3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c25ebe1bcb", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "If not set, a default image will be used.", "required": 0, "conditional_logic": [[{"field": "field_675c25ebe1bc3", "operator": "==", "value": "1"}, {"field": "field_675c25ebe1bca", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}]}, {"key": "field_675c260d2bae2", "label": "Bottom CTA", "name": "bottom_cta", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c260d2bae3", "label": "Status", "name": "status", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Active", "default_value": 1, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c260d2bae4", "label": "Title", "name": "title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c260d2bae3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Your journey starts here", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c260d2bae5", "label": "Text", "name": "text", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c260d2bae3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "We create award-winning holidays rooted in local knowledge. Enquire now for expert advice and a bespoke itinerary tailored just for you.", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c260d2bae6", "label": "Button label", "name": "button_label", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c260d2bae3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Make an Enquiry", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c260d2bae7", "label": "Button link", "name": "button_link", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c260d2bae3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "/make-an-enquiry/", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c260d2bae8", "label": "Icon", "name": "icon", "aria-label": "", "type": "text", "instructions": "HTML/SVG only.", "required": 0, "conditional_logic": [[{"field": "field_675c260d2bae3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "<svg class=\"svg-inline--fa fa-phone fa-w-16\" aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fas\" data-icon=\"phone\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" data-fa-i2svg=\"\"><path fill=\"currentColor\" d=\"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z\"></path></svg>", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c260d2bae9", "label": "Contact number", "name": "contact_number", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c260d2bae3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "+44 (0) ************", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c260d2baea", "label": "Use Image", "name": "use_image", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c260d2bae3", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 1, "ui": 0, "ui_on_text": "", "ui_off_text": ""}, {"key": "field_675c260d2baeb", "label": "Image", "name": "image", "aria-label": "", "type": "image", "instructions": "If not set, a default image will be used.", "required": 0, "conditional_logic": [[{"field": "field_675c260d2bae3", "operator": "==", "value": "1"}, {"field": "field_675c260d2baea", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "preview_size": "medium"}]}, {"key": "field_675c3010f2010", "label": "Featured Holidays Panel", "name": "featured_holidays_panel", "aria-label": "", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c3011f2011", "label": "Panel Status", "name": "panel_status", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "Show featured holidays panel", "default_value": 0, "ui_on_text": "On", "ui_off_text": "Off", "ui": 1}, {"key": "field_675c3012f2012", "label": "Panel Title", "name": "panel_title", "aria-label": "", "type": "text", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c3011f2011", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Our most popular holidays", "maxlength": "", "placeholder": "", "prepend": "", "append": ""}, {"key": "field_675c3013f2013", "label": "Featured Holidays", "name": "featured_holidays", "aria-label": "", "type": "repeater", "instructions": "Select up to 4 holidays to feature", "required": 0, "conditional_logic": [[{"field": "field_675c3011f2011", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "min": 1, "max": 4, "layout": "table", "button_label": "Add Holiday", "collapsed": "", "rows_per_page": 20, "sub_fields": [{"key": "field_675c3014f2014", "label": "Holiday", "name": "holiday", "aria-label": "", "type": "post_object", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["holiday"], "taxonomy": "", "return_format": "object", "multiple": 0, "allow_null": 0, "ui": 1, "bidirectional_target": [], "parent_repeater": "field_675c3013f2013"}]}]}, {"key": "field_675c5000f4001", "label": "Steps Ahead Panel", "name": "steps_ahead_panel", "aria-label": "", "type": "group", "instructions": "Content is managed centrally in Theme Settings. Only style controls are available here.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_675c5001f4002", "label": "Panel Status", "name": "panel_status", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "message": "", "default_value": 1, "ui_on_text": "On", "ui_off_text": "Off", "ui": 1}, {"key": "field_675c5004f4005", "label": "Panel Background", "name": "panel_background", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c5001f4002", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "#ebf2f1", "enable_opacity": 0, "return_format": "string"}, {"key": "field_675c5005f4006", "label": "Curve Position", "name": "curve_position", "aria-label": "", "type": "select", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c5001f4002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"none": "None", "top": "Top", "bottom": "Bottom"}, "default_value": "bottom", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_68557518df3d3", "label": "<PERSON><PERSON>ve <PERSON>", "name": "curve_flip", "aria-label": "", "type": "true_false", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c5001f4002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "", "ui_off_text": "", "ui": 1}, {"key": "field_68557538df3d4", "label": "Point Title", "name": "point_title", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c5001f4002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "#75ada4", "enable_opacity": 0, "return_format": "string"}, {"key": "field_68557549df3d5", "label": "Point Background", "name": "point_background", "aria-label": "", "type": "color_picker", "instructions": "", "required": 0, "conditional_logic": [[{"field": "field_675c5001f4002", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "enable_opacity": 0, "return_format": "string"}]}], "location": [[{"param": "taxonomy", "operator": "==", "value": "holiday-regions"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1751034075}