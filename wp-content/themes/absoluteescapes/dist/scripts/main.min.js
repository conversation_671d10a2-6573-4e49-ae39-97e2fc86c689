const DEBUG_MODE=!1,debug={log:(...e)=>DEBUG_MODE&&console.log(...e),warn:(...e)=>DEBUG_MODE&&console.warn(...e)};function trackEvents(e,t,o,a={}){if(debug.log("🎯 Initializing tracking:",{triggerEvent:e,selector:t,eventType:o,eventData:a}),!e&&!t)return debug.log("📡 Direct event firing mode"),void("function"==typeof gtag?(debug.log("📊 Sending direct event to GA:",{eventType:o,eventData:a}),gtag("event",o,a)):debug.warn("⚠️ gtag not found - event not sent"));const n=Array.isArray(t)?t:[t];debug.log("🎯 Tracking selectors:",n);const r=document.querySelectorAll(n.join(","));debug.log(`🔍 Found ${r.length} elements to track`),r.forEach(n=>{n.addEventListener(e,function(e){debug.log("🎯 Tracked element clicked:",n);var t={text:n.textContent.replace(/[\n\r\s]+/g," ").trim(),classes:n.classList.value,url:n.href||n.querySelector('a[href^="tel:"]')&&n.querySelector('a[href^="tel:"]').href||"",id:n.id};debug.log("📊 Element data:",t),"function"==typeof gtag?(t={...t,button_name:t.text,screen_name:window.location.pathname,...a},debug.log("📡 Sending event to GA:",{eventType:o,...t}),gtag("event",o,t)):debug.warn("⚠️ gtag not found - event not sent")})})}debug.log("🚀 Analytics tracking script loaded"),document.addEventListener("DOMContentLoaded",function(){debug.log("📱 Setting up phone link tracking"),trackEvents("click",[".masthead__details-item.masthead__phone",".mastfoot__contact-item.mastfoot__phone",'.listing-cta.cta-top a[href^="tel:"]','.listing-cta.cta-bottom a[href^="tel:"]'],"button_click")}),jQuery(document).on("gform_confirmation_loaded",function(e,t){if(debug.log("📝 Gravity Form confirmation loaded:",t),1===t||7===t){debug.log("📝 Processing form submission:",t);const o=jQuery(`#gform_confirmation_wrapper_${t}`);var n={form_id:t,form_message:o.text().replace(/[\n\r\s]+/g," ").trim(),screen_name:window.location.pathname};debug.log("📝 Form submitted with parameters:",n),"function"==typeof gtag&&gtag("event","form_submit",n)}else debug.log("ℹ️ Ignoring non-tracked form:",t)});
document.addEventListener("DOMContentLoaded",function(o){geoip_detect.get_info().then(function(o){if(o.error())return console.error("WARNING Geodata Error:"+o.error()),void console.log(o.error());var t=o.get_with_locales("country.isoCode",["en"]);const e=document.querySelectorAll('form[id^="gform_"]');e.forEach(o=>{const e=o.querySelector(".geoip_country");if(e){const r=e.querySelector("input");r&&(r.value=t)}})}).catch(o=>{console.error("Error in geoip detection:",o)})});
$(function(){var a=$("input[value='holiday']"),n=$("input[value='holiday_title']"),o=$("input[value='holiday_type']"),i=$("input[value='tourcode']"),c=$("input[value='itinerary']"),s=$("input[value='accommodation']"),l=$("input[value='startdate']");jQuery(document).on("gform_post_render",function(t,e){1!==e&&($(".gfmc-column").append('<div class="form__next"><span class="button button--alt">Next <i class="fas fa-chevron-right"></i></span><div>'),$(".form").find(".gform_footer").appendTo(".gfmc-column:last"),$(".gform_confirmation_wrapper").length&&($(".form__section").remove(),$(".form__heading-wrapper").remove()),$(".gsection").first().addClass("active"),$(".gsection").first().next().addClass("active"),$(".validation_message").length&&($(".validation_message").closest(".gfmc-column").addClass("active"),$(".validation_message").closest(".gfmc-column").prev().addClass("active"))),2===e&&(a.val($(".form__section").attr("data-id")),n.val($(".form__section").attr("data-title")),o.val($(".form__section").attr("data-type")),i.val($(".tour-code").text()),c.val($("#formItinerary").val()),s.val($("#formAccommodation").val()),l.val($("#formStart").val()),$("#formItinerary").on("change",function(){c.val($(this).val())}),$("#formAccommodation").on("change",function(){s.val($(this).val())}),$("#formStart").on("change",function(){l.val($(this).val())})),$(".validation_error").length&&$(".payment-choice").find("input").attr("checked",!1),$(document).find(".payment-choice").find("input").on("change",function(){"bank transfer"!==$(this).val().toLowerCase()?($(".form--payment").find(".gsection").show(),$(".payment-details__notice").removeClass("active"),$(".gfmc-row-2-column").addClass("active"),$(".gfmc-row-2-column").prev().addClass("active")):($(".form--payment").find(".gsection").hide(),$(".form--payment").find(".gfmc-column").removeClass("active"),$(".form--payment").find(".gsection").removeClass("active"),$(".payment-details__notice").addClass("active"),$(".gsection").first().addClass("active"))})}),gform.addFilter("gform_datepicker_options_pre_init",function(t,e,a){return t.yearRange="-0:+5",t}),$(document).on("gform_confirmation_loaded",function(t,e){2!==e&&3!==e||$(".gform_confirmation_wrapper").length&&($(".form__section").remove(),$(".form__heading-wrapper").remove())}),$(document).on("click",".form__next .button",function(){var t=$(this);$(this).closest(".gfmc-column").removeClass("active"),$(this).closest(".gfmc-column").next().is(":visible")?($(this).closest(".gfmc-column").next().addClass("active"),$(this).closest(".gfmc-column").next().next().addClass("active")):($(this).closest(".gfmc-column").next().next().next().addClass("active"),$(this).closest(".gfmc-column").next().next().next().next().addClass("active")),$("html, body").animate({scrollTop:t.closest(".gfmc-column").next().offset().top},0)}),$(document).on("click",".gsection",function(){$(this).next().hasClass("active")?$(this).next().removeClass("active"):($(this).next().addClass("active"),$(this).addClass("active"))}),$("#filterTrigger").on("click",function(t){t.preventDefault(),$(".filter").slideToggle()}),$(".enquiry-form").on("click",function(t){t.target===this&&$(this).removeClass("active")}),$(".enquiry-cta__button").on("click",function(t){t.preventDefault(),$(".enquiry-form").toggleClass("active")}),$(".enquiry-form__close").on("click",function(){$(".enquiry-form").removeClass("active")})});
$(window).on("load",function(){var t=$(".subnav");t.length&&Stickyfill.add(t);var i=$(".enquiry-form");i.length&&Stickyfill.add(i);var n=function(t){for(var i,n=window.location.search.substring(1).split("&"),e=0;e<n.length;e++)if((i=n[e].split("="))[0]===t)return void 0===i[1]||decodeURIComponent(i[1])};function e(t){var i=t.getBoundingClientRect(),n=window.innerHeight||document.documentElement.clientHeight,t=window.innerWidth||document.documentElement.clientWidth,n=i.top<=n&&0<=i.top+(i.height-116),i=i.left<=t&&0<=i.left+i.width;return n&&i}$(document).on("click",".masthead a",function(t){!/#/.test(this.href)||$(this).hasClass("aito-link")||$(this).hasClass("modal-close")||t.preventDefault()}),$(".aito-link").on("click",function(){$(".review-bar").addClass("active")}),$(".modal-close").on("click",function(){$(".review-bar").removeClass("active")}),$(".scroll-next").on("click",function(){$("html, body").animate({scrollTop:$(this).closest("section").next().offset().top-50},500)}),$(".scroll-to").on("click",function(){var t=$(this).data("target");!t||(t=$("#"+t)).length&&$("html, body").animate({scrollTop:t.offset().top-50},500)}),$(".back-to-top").on("click",function(){$("html, body").animate({scrollTop:$("html").offset().top},1e3)});t=$(".reviews__row");function o(){$(".favourite-holidays__title").each(function(){var t=$(this);t.data("original-text")||(t.data("original-text",t.text().trim()),t.data("original-html",t.html().trim()));var i=t.data("original-text"),n=t.data("original-html");if(t.html(n),!(n.includes("<br>")||n.includes("<BR>")||i.includes("\n")||i.includes("\r"))){var e,o,a,l=t.height(),s=parseFloat(t.css("line-height")),s=1.3*(s=!s||0===s?1.2*parseFloat(t.css("font-size")):s)<l;if(s||3<=(a=i.split(/\s+/)).length&&((e=t.clone()).css({position:"absolute",visibility:"hidden","white-space":"nowrap",width:"auto",height:"auto"}),e.appendTo(t.parent()),e.html(i),l=e.width(),o=t.width(),e.remove(),o<l&&(s=!0)),s)3<=(a=i.split(/\s+/)).length?((e=t.clone()).css({position:"absolute",visibility:"hidden","white-space":"nowrap",width:"auto",height:"auto"}),e.appendTo(t.parent()),s=a.slice(0,-1).join(" "),e.html(s),s=e.width(),o=t.width(),e.remove(),s<=o?(h=(r=a.slice(0,-2).join(" "))+"<br>"+(c=a.slice(-2).join(" ")),t.html(h)):t.html(n)):t.html(n);else if(3<=(a=i.split(/\s+/)).length){for(var r,c,d=!1,f=0;f<a.length-1;f++)if(a[f].length<4){d=!0;break}c=d?(r=a.slice(0,-1).join(" "),a.slice(-1).join(" ")):(r=a.slice(0,-2).join(" "),a.slice(-2).join(" "));var h=r+"<br>"+c;t.html(h)}}})}function a(){var t=$(".favourite-holidays__col-content"),i=$(".favourite-holidays__background"),n=0;i.css("height",""),t.css("min-height",""),t.each(function(){var t=$(this).outerHeight();n<t&&(n=t)}),t.css("min-height",n+"px");t=Math.max(n+40,445);i.css("height",t+"px")}t.on("ready.flickity",function(){$(this).parent().find(".reviews__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".reviews__button--next").appendTo($(this).find(".flickity-button.next"))}),t.flickity({wrapAround:!0,contain:!0,freeScroll:!0});i=$(".favourite-holidays__row");i.on("ready.flickity",function(){$(this).parent().find(".favourite-holidays__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".favourite-holidays__button--next").appendTo($(this).find(".flickity-button.next")),setTimeout(function(){o(),a()},100)}),i.on("change.flickity",function(){setTimeout(function(){o(),a()},50)}),i.flickity({freeScroll:!0,groupCells:!0});var l,s,r,t=(l=function(){$(".favourite-holidays__row").length&&(o(),a())},s=250,function(){var t=this,i=arguments;clearTimeout(r),r=setTimeout(function(){r=null,l.apply(t,i)},s)});$(window).on("resize",t);i=$(".destinations__row");i.on("ready.flickity",function(){$(this).parent().find(".favourite-holidays__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".favourite-holidays__button--next").appendTo($(this).find(".flickity-button.next"))}),i.flickity({wrapAround:!0,pageDots:!1,freeScroll:!0,contain:!0,groupCells:!0,cellAlign:"left"});t=$(".instagram__photos");t.on("ready.flickity",function(){$(this).parent().find(".instagram__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".instagram__button--next").appendTo($(this).find(".flickity-button.next"))}),t.flickity({pageDots:!1,freeScroll:!0,contain:!0,groupCells:!0,cellAlign:"left"});i=$(".carousel__images");i.on("ready.flickity",function(){$(this).parent().find(".carousel__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".carousel__button--next").appendTo($(this).find(".flickity-button.next"))}),i.flickity({wrapAround:!0,contain:!0,groupCells:!0}),$(".holidays__gallery").flickity({wrapAround:!0,contain:!0,groupCells:!0,arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:35,x3:35}});t=$(".accommodation__images");t.on("ready.flickity",function(){$(this).parent().find(".accommodation__button--prev").appendTo($(this).find(".flickity-button.previous")),$(this).parent().find(".accommodation__button--next").appendTo($(this).find(".flickity-button.next"))}),t.flickity({wrapAround:!0,contain:!0,groupCells:!0});var c=$(".inf-posts");$(".next-posts-link").length&&c.length&&(c.infiniteScroll({path:".next-posts-link a",append:".inf-posts .inf-post",history:!0,button:".button-inf",scrollThreshold:!1,status:".page-load-status"}),c.on("append.infiniteScroll",function(t,i,n,e){$(e).addClass("appended-item"),c.imagesLoaded(function(){$(e).find("img").each(function(t,i){i.outerHTML=i.outerHTML}),$(document).find(".holidays__gallery").length&&$(document).find(".holidays__gallery").flickity({wrapAround:!0,contain:!0,groupCells:!0,arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:35,x3:35}}),g()})}));i=document.getElementById("distanceRangeSlider");$("#distanceRangeSlider").length&&(noUiSlider.create(i,{start:[1,32],connect:!0,step:1,range:{min:1,max:32}}),t=n("durationmin"),n=n("durationmax"),t&&n?i.noUiSlider.set([t,n]):t?i.noUiSlider.set([t,null]):n&&i.noUiSlider.set([null,n]),i.noUiSlider.on("update",function(t,i){$(".filter__range-number--min").text(Math.floor(t[0])),$("#durationMin").val(Math.floor(t[0])),$(".filter__range-number--max").text(Math.floor(t[1])),$("#durationMax").val(Math.floor(t[1]))}),$("#orderDropdown").on("change",function(){$("#sort").val($(this).val()),$("#filterForm").submit()}),i.noUiSlider.on("change",function(){$("#filterForm").submit()})),$(".filter__input").on("change",function(){$("#filterForm").submit()});var d=!1;$(".filter__label-wrapper").on("click",function(){var t=$(this);d||(d=!0,t.hasClass("collapsed")?t.next().slideDown(function(){t.removeClass("collapsed"),d=!1}):t.next().slideUp(function(){t.addClass("collapsed"),d=!1}))});var f,h=[];$(".page-header__gallery").find("img").each(function(){(f={}).src=$(this).attr("src"),h.push(f)}),$("#galleryTrigger").on("click",function(t){t.preventDefault(),$.fancybox.open(h,{loop:!0}),$('[data-fancybox="gallery"]').fancybox({afterLoad:function(t,i){i.$image.attr("alt",i.opts.$orig.find("img").attr("alt"))}})});var u=!1;$(".accordion__heading-wrapper").on("click",function(){var t=$(this);u||(u=!0,t.parent().hasClass("active")?(t.parent().removeClass("active"),t.next().slideUp(function(){u=!1})):(t.parent().addClass("active"),t.next().slideDown(function(){u=!1})))}),$(".accordion__close").on("click",function(){var t;u||(u=!0,(t=$(this)).parent().parent().slideUp(function(){u=!1,t.parent().parent().parent().removeClass("active")}))}),$("#subNav").on("change",function(){var t=$(this).val();$("html, body").animate({scrollTop:$("#"+t).offset().top-100},0)}),$(".subnav").length&&(setTimeout(function(){$(this).scrollTop()<=150?($(".subnav__link").parent().removeClass("active"),$("#subNav").val("")):$("section").each(function(){if(""!==$(this).prop("id")){var t=$(this);if(e(this))return $(".subnav__link").each(function(){$(this).attr("data-id")===t.prop("id")?($(".subnav__link[data-id="+t.prop("id")+"]").parent().addClass("active"),$("#subNav").val(t.prop("id"))):($(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active"),$("#subNav").val(""))}),!1;$(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active"),$("#subNav").val("")}})},50),$(window).on("scroll",function(){$(this).scrollTop()<=150?($(".subnav__link").parent().removeClass("active"),$("#subNav").val("")):$("section").each(function(){if(""!==$(this).prop("id")){var t=$(this);if(e(this))return $(".subnav__link").each(function(){$(this).attr("data-id")===t.prop("id")?($(".subnav__link[data-id="+t.prop("id")+"]").parent().addClass("active"),$("#subNav").val(t.prop("id"))):$(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active")}),!1;$(".subnav__link").not(".subnav__link[data-id="+t.prop("id")+"]").parent().removeClass("active"),$("#subNav").val("")}})})),$('[data-toggle="datepicker"]').datepicker({format:"dd/mm/yyyy",autoHide:!0}),$(".tooltip").on("click",function(){$(this).toggleClass("active")}),$(".search-trigger").on("click",function(){$(".masthead__form-container").toggleClass("active"),$(".masthead__form-container").hasClass("active")&&$(".masthead__form > input").focus()}),$(".masthead__form-close").on("click",function(){$(".masthead__form-container").removeClass("active")});var p=!1;if($(".overview__bottom-copy-trigger").on("click",function(){p||(p=!0,$(this).hasClass("active")?($(this).removeClass("active"),$(this).next().slideUp(function(){p=!1})):($(this).addClass("active"),$(this).next().slideDown(function(){p=!1})))}),$(".banner__slideshow").length){var v=$(".banner__slideshow"),i=v.find(".banner__slide").length;if(console.log("Banner slideshow found. Slide count:",i),1<i)try{v.addClass("fade-enabled"),v.flickity({autoPlay:6e3,wrapAround:!0,pauseAutoPlayOnHover:!1,prevNextButtons:!1,pageDots:!1,draggable:!1,accessibility:!1,setGallerySize:!1}),console.log("Flickity initialized with autoPlay: 6000"),v.on("ready.flickity",function(){var t=v.find(".banner__slide");t.each(function(t){var i=$(this);i.css({position:"absolute",top:"0",left:"0",width:"100%",height:"100%",transform:"none"}),0<t&&i.css("opacity","0")});t=t.filter(".is-selected");t.css("opacity","1"),v.find(".flickity-slider").css("transform","none");t=t.find(".banner__heading");t.length&&t.css({opacity:"0"}).animate({opacity:"1"},800)}),v.on("change.flickity",function(){var t=v.find(".banner__slide"),i=t.filter(".is-selected");v.find(".flickity-slider").css("transform","none"),t.css({position:"absolute",top:"0",left:"0",width:"100%",height:"100%",opacity:"0",transform:"none"}),i.css("opacity","1");var n=i.find(".banner__heading");n.length&&(n.css({opacity:"0"}),setTimeout(function(){n.animate({opacity:"1"},600)},200))})}catch(t){console.error("Error initializing Flickity:",t)}else{var _=v.find(".banner__slide").find(".banner__heading");_.length&&(_.css({opacity:"0"}),setTimeout(function(){_.animate({opacity:"1"},800)},500))}}function g(){$(".holidays__post-row").off("click.holidayRow").on("click.holidayRow",function(t){$(t.target).closest(".holidays__gallery, a, .flickity-button").length||(t=$(this).find(".holidays__title").closest("a")).length&&t.attr("href")&&(window.location.href=t.attr("href"))}),$(".holidays__gallery .holidays__image").off("click.galleryImage").on("click.galleryImage",function(t){$(t.target).closest(".flickity-button").length||(t=$(this).closest(".holidays__post-row").find(".holidays__title").closest("a")).length&&t.attr("href")&&(window.location.href=t.attr("href"))})}g(),AOS.init({once:!0}),$(".overlay").fadeOut()});
$(function(){var a=$(".masthead"),s=$(".masthead__burger"),e=$(".navigation");s.on("click",function(){$(this).toggleClass("active"),$(this).hasClass("active")?(a.addClass("active-nav"),e.addClass("active"),$("html, body").addClass("overflow-menu")):(a.removeClass("active"),e.removeClass("active"),$("html, body").removeClass("overflow-menu"))}),$(".navigation__close").on("click",function(){a.removeClass("active"),e.removeClass("active"),e.removeClass("submenu-active"),$("html, body").removeClass("overflow-menu"),s.removeClass("active"),$(".sub-menu").removeClass("active")}),$(".navigation").find(".menu-item-has-children").append('<span class="sub-arrow"></span>'),$(".navigation").find(".sub-menu").each(function(){var s=$(this).prev().text();$(this).prepend('<li><a class="back-link" href="#">< '+s+"</a></li>")}),$(document).on("click",".sub-arrow",function(){$(this).prev().addClass("active"),e.addClass("submenu-active"),e.scrollTop(0)}),$(document).on("click",".back-link",function(){$(this).parent().parent().removeClass("active"),$(this).parent().parent().parent().parent().hasClass("menu")&&e.removeClass("submenu-active"),e.scrollTop(0)}),$(".navigation").find("a[href*='#']").on("click",function(){$(this).hasClass("back-link")||($(this).next().addClass("active"),e.addClass("submenu-active"),e.scrollTop(0))});var i=!1;$(".mastfoot").find(".menu-item-has-children > a").on("click",function(s){s.preventDefault(),i||(i=!0,$(this).hasClass("active")?($(this).removeClass("active"),$(this).next(".sub-menu").slideUp(function(){i=!1})):($(this).addClass("active"),$(this).next(".sub-menu").slideDown(function(){i=!1})))});var n=0;$(window).on("scroll",function(){var s;a.hasClass("masthead--static")||(150<(s=$(this).scrollTop())?(a.addClass("is-fixed"),n<s?a.hasClass("is-visible")&&(a.addClass("is-hidden"),a.removeClass("is-visible")):(a.removeClass("is-hidden"),a.addClass("is-visible"))):s<1&&(a.removeClass("is-fixed"),a.removeClass("is-hidden"),a.removeClass("is-visible")),n=s)})});
$(function(){$(".filter-links").move({breakpoint:991,oldLocation:".holidays-results",newLocation:".filter",methods:{o:"insertBefore",n:"insertAfter"}}),$(".holidays-results__sort-by-wrapper").move({breakpoint:991,oldLocation:".holidays-results__order",newLocation:".holidays-results__sort-by-trigger-wrapper",methods:{o:"appendTo",n:"appendTo"}}),$(".masthead__form-header").move({breakpoint:1160,oldLocation:".masthead__details",newLocation:".form__container",methods:{o:"insertBefore",n:"prependTo"}})});